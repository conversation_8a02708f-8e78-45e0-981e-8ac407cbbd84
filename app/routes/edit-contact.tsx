import { Form, redirect, useNavigate } from "react-router";
import { useState } from "react";

import { getContact, updateContact } from "../data";
import type { Route } from "./+types/edit-contact";
import {
  queryClient,
  handleQueryError,
  handleMutationError,
} from "../lib/query-client";
import { SectionErrorBoundary } from "../components/error-boundary";
import {
  contactValidationSchema,
  handleFormSubmission,
  useFormValidation,
} from "../lib/validation";
import { useErrorToast } from "../components/toast";
import { ValidationError } from "../lib/errors";

export async function clientLoader({ params }: Route.LoaderArgs) {
  try {
    const contact = await queryClient.fetchQuery({
      queryKey: ["contact-details", params.contactId],
      queryFn: () => getContact(params.contactId),
    });

    return { contact };
  } catch (error) {
    throw handleQueryError(error, ["contact-details", params.contactId]);
  }
}

export async function clientAction({ params, request }: Route.ActionArgs) {
  try {
    const formData = await request.formData();

    // Use enhanced form submission with validation
    await handleFormSubmission(formData, contactValidationSchema, (data) =>
      updateContact(params.contactId, data)
    );

    queryClient.invalidateQueries({ queryKey: ["contacts"] });
    queryClient.invalidateQueries({ queryKey: ["contact-details"] });

    return redirect(`/contacts/${params.contactId}`);
  } catch (error) {
    throw handleMutationError(error, ["update-contact", params.contactId]);
  }
}

export default function EditContact({ loaderData }: Route.ComponentProps) {
  const { contact } = loaderData;
  const navigate = useNavigate();
  const showErrorToast = useErrorToast();

  // Form state
  const [formData, setFormData] = useState({
    first: contact.first || "",
    last: contact.last || "",
    twitter: contact.twitter || "",
    avatar: contact.avatar || "",
    notes: contact.notes || "",
  });

  // Validation
  const {
    validateField,
    validateForm,
    getFieldError,
    hasFieldError,
    markFieldTouched,
  } = useFormValidation(contactValidationSchema);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    validateField(field, value);
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // Validate all fields
    const validation = validateForm(formData);

    if (!validation.isValid) {
      // Mark all fields as touched to show errors
      Object.keys(formData).forEach(markFieldTouched);
      showErrorToast(
        new ValidationError(
          validation.firstError || "Please fix the validation errors"
        )
      );
      return;
    }

    // Submit the form data
    const form = e.currentTarget;
    const formDataToSubmit = new FormData(form);

    try {
      await handleFormSubmission(
        formDataToSubmit,
        contactValidationSchema,
        (data) => updateContact(contact.id, data)
      );

      queryClient.invalidateQueries({ queryKey: ["contacts"] });
      queryClient.invalidateQueries({ queryKey: ["contact-details"] });

      navigate(`/contacts/${contact.id}`);
    } catch (error) {
      showErrorToast(error as Error);
    }
  };

  return (
    <SectionErrorBoundary title="Edit Contact">
      <Form key={contact.id} id="contact-form" onSubmit={handleSubmit}>
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            marginBottom: "1rem",
          }}
        >
          <span style={{ marginBottom: "0.5rem" }}>Name</span>
          <div
            style={{
              display: "flex",
              gap: "0.5rem",
              marginBottom: "0.25rem",
            }}
          >
            <div style={{ flex: 1 }}>
              <input
                aria-label="First name"
                value={formData.first}
                onChange={(e) => handleInputChange("first", e.target.value)}
                onBlur={() => markFieldTouched("first")}
                name="first"
                placeholder="First"
                type="text"
                style={{
                  width: "100%",
                  height: "2.5rem",
                  border: hasFieldError("first")
                    ? "2px solid #dc3545"
                    : "1px solid #ccc",
                  borderRadius: "4px",
                  padding: "0.5rem",
                  boxSizing: "border-box",
                  fontSize: "1rem",
                  lineHeight: "1.5",
                }}
              />
            </div>
            <div style={{ flex: 1 }}>
              <input
                aria-label="Last name"
                value={formData.last}
                onChange={(e) => handleInputChange("last", e.target.value)}
                onBlur={() => markFieldTouched("last")}
                name="last"
                placeholder="Last"
                type="text"
                style={{
                  width: "100%",
                  height: "2.5rem",
                  border: hasFieldError("last")
                    ? "2px solid #dc3545"
                    : "1px solid #ccc",
                  borderRadius: "4px",
                  padding: "0.5rem",
                  boxSizing: "border-box",
                  fontSize: "1rem",
                  lineHeight: "1.5",
                }}
              />
            </div>
          </div>
          <div
            style={{
              height: "1.25rem",
              display: "flex",
              gap: "0.5rem",
            }}
          >
            <div style={{ flex: 1 }}>
              {getFieldError("first") && (
                <span
                  style={{
                    color: "#dc3545",
                    fontSize: "0.875rem",
                    lineHeight: "1.25rem",
                    whiteSpace: "nowrap",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    display: "block",
                  }}
                >
                  {getFieldError("first")}
                </span>
              )}
            </div>
            <div style={{ flex: 1 }}>
              {getFieldError("last") && (
                <span
                  style={{
                    color: "#dc3545",
                    fontSize: "0.875rem",
                    lineHeight: "1.25rem",
                    whiteSpace: "nowrap",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    display: "block",
                  }}
                >
                  {getFieldError("last")}
                </span>
              )}
            </div>
          </div>
        </div>
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            marginBottom: "1rem",
          }}
        >
          <span style={{ marginBottom: "0.5rem" }}>Twitter</span>
          <input
            value={formData.twitter}
            onChange={(e) => handleInputChange("twitter", e.target.value)}
            onBlur={() => markFieldTouched("twitter")}
            name="twitter"
            placeholder="@jack"
            type="text"
            style={{
              width: "100%",
              height: "2.5rem",
              border: hasFieldError("twitter")
                ? "2px solid #dc3545"
                : "1px solid #ccc",
              borderRadius: "4px",
              padding: "0.5rem",
              marginBottom: "0.25rem",
              boxSizing: "border-box",
              fontSize: "1rem",
              lineHeight: "1.5",
            }}
          />
          <div style={{ height: "1.25rem" }}>
            {getFieldError("twitter") && (
              <span
                style={{
                  color: "#dc3545",
                  fontSize: "0.875rem",
                  lineHeight: "1.25rem",
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  display: "block",
                }}
              >
                {getFieldError("twitter")}
              </span>
            )}
          </div>
        </div>
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            marginBottom: "1rem",
          }}
        >
          <span style={{ marginBottom: "0.5rem" }}>Avatar URL</span>
          <input
            aria-label="Avatar URL"
            value={formData.avatar}
            onChange={(e) => handleInputChange("avatar", e.target.value)}
            onBlur={() => markFieldTouched("avatar")}
            name="avatar"
            placeholder="https://example.com/avatar.jpg"
            type="url"
            style={{
              width: "100%",
              height: "2.5rem",
              border: hasFieldError("avatar")
                ? "2px solid #dc3545"
                : "1px solid #ccc",
              borderRadius: "4px",
              padding: "0.5rem",
              marginBottom: "0.25rem",
              boxSizing: "border-box",
              fontSize: "1rem",
              lineHeight: "1.5",
            }}
          />
          <div style={{ height: "1.25rem" }}>
            {getFieldError("avatar") && (
              <span
                style={{
                  color: "#dc3545",
                  fontSize: "0.875rem",
                  lineHeight: "1.25rem",
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  display: "block",
                }}
              >
                {getFieldError("avatar")}
              </span>
            )}
          </div>
        </div>
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            marginBottom: "1rem",
          }}
        >
          <span style={{ marginBottom: "0.5rem" }}>Notes</span>
          <textarea
            value={formData.notes}
            onChange={(e) => handleInputChange("notes", e.target.value)}
            onBlur={() => markFieldTouched("notes")}
            name="notes"
            rows={6}
            style={{
              border: hasFieldError("notes")
                ? "2px solid #dc3545"
                : "1px solid #ccc",
              borderRadius: "4px",
              padding: "0.5rem",
              marginBottom: "0.25rem",
              resize: "vertical",
            }}
          />
          <div style={{ height: "1.25rem" }}>
            {getFieldError("notes") && (
              <span
                style={{
                  color: "#dc3545",
                  fontSize: "0.875rem",
                  lineHeight: "1.25rem",
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  display: "block",
                }}
              >
                {getFieldError("notes")}
              </span>
            )}
          </div>
        </div>
        <div>
          <button type="submit">Save</button>&nbsp;
          <button onClick={() => navigate(-1)} type="button">
            Cancel
          </button>
        </div>
      </Form>
    </SectionErrorBoundary>
  );
}
